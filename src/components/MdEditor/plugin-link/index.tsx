/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable indent */
import { expectDomTypeError } from '@milkdown/exception';
import { toggleMark } from '@milkdown/prose/commands';
import type { Node as ProseNode } from '@milkdown/prose/model';
import { TextSelection, Plugin } from '@milkdown/prose/state';
import { $command, $markSchema } from '@milkdown/utils';
import type { Meta, MilkdownPlugin } from '@milkdown/ctx';
import { linkAttr } from '@milkdown/preset-commonmark';

export function withMeta<T extends MilkdownPlugin>(
  plugin: T,
  meta: Partial<Meta> & Pick<Meta, 'displayName'>
): T {
  Object.assign(plugin, {
    meta: {
      package: '@milkdown/preset-commonmark',
      ...meta,
    },
  });

  return plugin;
}

withMeta(linkAttr, {
  displayName: 'Attr<link>',
  group: 'Link',
});

/// Link mark schema.
export const linkSchema = $markSchema('link', (ctx) => {
  const schema = {
    attrs: {
      href: {},
      title: { default: null },
      target: { default: '_blank' },
    },
    inclusive: false,
    parseDOM: [
      {
        tag: 'a[href]',
        getAttrs: (dom) => {
          if (!(dom instanceof HTMLElement)) {
            throw expectDomTypeError(dom);
          }
          return {
            href: dom.getAttribute('href'),
            title: dom.getAttribute('title'),
            target: dom.getAttribute('target') || '_blank',
          };
        },
      },
    ],
    toDOM: (mark) => {
      const attrs = { ...ctx.get(linkAttr.key)(mark), ...mark.attrs };
      const href = attrs.href || '';

      // 检查href是否包含有效的URL，并在遇到中文字符（包括URL编码的中文）时停止匹配
      const urlRegex =
        /(https?:\/\/[^\s\u4e00-\u9fff\u3000-\u303f\uff00-\uffef，。！？；：""''（）【】《》]+)/;

      // 先尝试直接匹配，如果包含URL编码的中文字符，则手动截取
      const urlMatch = href.match(urlRegex);
      let cleanUrl = '';

      if (urlMatch) {
        const matchedUrl = urlMatch[0];
        // 检查是否包含URL编码的中文字符（通常以%E开头）
        const chineseEncodedMatch = matchedUrl.match(/(.*?)%[E-F][0-9A-F]/);
        if (chineseEncodedMatch) {
          // 如果找到URL编码的中文字符，截取到该位置之前
          cleanUrl = chineseEncodedMatch[1];
        } else {
          // 否则使用完整匹配
          cleanUrl = matchedUrl;
        }
      }

      if (cleanUrl) {
        // 找到有效URL

        // 检查href是否只是一个纯净的URL（没有额外的中文字符）
        const isCleanUrl = href.trim() === cleanUrl;

        if (isCleanUrl) {
          // 如果href就是一个纯净的URL，a标签显示cleanUrl
          return ['span', ['a', { ...attrs, href: cleanUrl }, cleanUrl]];
        } else {
          // 如果href包含额外字符，提取并分离
          const extraChars = href.replace(cleanUrl, '').trim();
          const aAttrs = {
            href: cleanUrl,
            target: attrs.target || '_blank',
            ...(attrs.title && { title: attrs.title }),
          };

          if (extraChars) {
            // 解码URL编码的中文字符
            let decodedExtraChars = '';
            try {
              decodedExtraChars = decodeURIComponent(extraChars);
            } catch (e) {
              // 如果解码失败，使用原始字符
              decodedExtraChars = extraChars;
            }

            // 有额外字符时：<div><a href="cleanUrl">cleanUrl</a>解码后的中文</div>
            return ['div', ['a', aAttrs, cleanUrl], decodedExtraChars];
          } else {
            // 没有额外字符时：<span><a href="cleanUrl">cleanUrl</a></span>
            return ['span', ['a', aAttrs, cleanUrl]];
          }
        }
      } else {
        // 没有找到有效URL，只返回span包含文本内容（不创建链接）
        return ['span', 0];
      }
    },
    parseMarkdown: {
      match: (node) => node.type === 'link',
      runner: (state, node, markType) => {
        const url = node.url as string;
        const title = node.title as string;
        const target = (node.target as string) || '_blank';
        state.openMark(markType, { href: url, title, target });
        state.next(node.children);
        state.closeMark(markType);
      },
    },
    toMarkdown: {
      match: (mark) => mark.type.name === 'link',
      runner: (state, mark) => {
        state.withMark(mark, 'link', undefined, {
          title: mark.attrs.title,
          url: mark.attrs.href,
          target: mark.attrs.target,
        });
      },
    },
  };
  return schema;
});

withMeta(linkSchema.mark, {
  displayName: 'MarkSchema<link>',
  group: 'Link',
});

/// @internal
export interface UpdateLinkCommandPayload {
  href?: string;
  title?: string;
  target?: string;
}
/// A command to toggle the link mark.
/// You can pass the `href` and `title` to the link.
export const toggleLinkCommand = $command(
  'ToggleLink',
  (ctx) =>
    (payload: UpdateLinkCommandPayload = {}) =>
      toggleMark(linkSchema.type(ctx), payload)
);

withMeta(toggleLinkCommand, {
  displayName: 'Command<toggleLinkCommand>',
  group: 'Link',
});

/// A command to update the link mark.
/// You can pass the `href` and `title` to update the link.
export const updateLinkCommand = $command(
  'UpdateLink',
  (ctx) =>
    (payload: UpdateLinkCommandPayload = {}) =>
    (state, dispatch) => {
      if (!dispatch) {
        return false;
      }

      let node: ProseNode | undefined;
      let pos = -1;
      const { selection } = state;
      const { from, to } = selection;
      state.doc.nodesBetween(from, from === to ? to + 1 : to, (n, p) => {
        if (linkSchema.type(ctx).isInSet(n.marks)) {
          node = n;
          pos = p;
          return false;
        }

        return undefined;
      });

      if (!node) {
        return false;
      }

      const mark = node.marks.find(({ type }) => type === linkSchema.type(ctx));
      if (!mark) {
        return false;
      }

      const start = pos;
      const end = pos + node.nodeSize;
      const { tr } = state;
      const linkMark = linkSchema
        .type(ctx)
        .create({ ...mark.attrs, ...payload });
      if (!linkMark) {
        return false;
      }

      dispatch(
        tr
          .removeMark(start, end, mark)
          .addMark(start, end, linkMark)
          .setSelection(new TextSelection(tr.selection.$anchor))
          .scrollIntoView()
      );

      return true;
    }
);

withMeta(updateLinkCommand, {
  displayName: 'Command<updateLinkCommand>',
  group: 'Link',
});
